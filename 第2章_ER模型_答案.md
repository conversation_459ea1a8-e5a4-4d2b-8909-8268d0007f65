# 第2章 ER模型 - 答案

### 1. 数据库结构设计包括哪几个阶段？每个阶段的任务是什么？
1. **概念结构设计**：建立概念模型，独立于具体DBMS
2. **逻辑结构设计**：转换为特定数据模型的逻辑结构
3. **物理结构设计**：确定数据的物理存储结构

### 2. 数据库概念模型设计的主要任务是什么？
- 分析用户需求，确定实体、属性和联系
- 建立ER图或其他概念模型
- 消除冗余，优化模型结构
- 与用户确认模型的正确性

### 3. 数据库逻辑结构设计的主要任务是什么？
- 将概念模型转换为逻辑模型
- 优化关系模式，消除数据冗余
- 进行规范化处理
- 设计视图和安全性策略

### 4. 数据库物理结构设计的主要任务是什么？
- 确定数据的存储结构
- 设计索引和聚簇
- 估算存储空间
- 优化存取路径和性能

### 5. 请介绍ER模型的主要知识要点。
- **实体（Entity）**：现实世界中的对象
- **属性（Attribute）**：实体的特征
- **联系（Relationship）**：实体间的关联
- **实体集**：同类实体的集合
- **联系集**：同类联系的集合
- **键**：唯一标识实体的属性组合

### 6. 在ER模型的扩展表示中，什么是弱实体？请给出弱实体的例子。
弱实体是不能被自己的属性唯一标识的实体，必须依赖于其他实体（强实体）才能唯一标识。

**例子**：
- 订单明细实体依赖于订单实体
- 员工家属实体依赖于员工实体
- 课程章节实体依赖于课程实体

### 7. 在ER模型的扩展表示中，什么是概括？请给出概括的例子。
概括（泛化）是将多个实体的共同特征抽象出来形成高层实体的过程。

**例子**：
- 学生、教师、职工可以概括为人员实体
- 轿车、卡车、客车可以概括为车辆实体
- 储蓄账户、支票账户可以概括为银行账户实体

### 8. 在ER模型的扩展表示中，什么是聚集？请给出聚集的例子。
聚集是将实体和联系看作高层实体的抽象机制，用于处理复杂的多元联系。

**例子**：
- 学生选课联系可以作为一个聚集，与教师实体建立"授课"联系
- 供应商供应零件给项目的三元联系可以聚集为一个实体，再与其他实体建立联系

### 9. ER模型向关系模型进行转换的要点包括哪一些？
- **实体转换**：每个实体转换为一个关系表
- **属性转换**：实体的属性转换为表的列
- **1:1联系**：可合并到任一实体表中，或创建独立表
- **1:N联系**：在N端实体表中加入1端实体的主键作为外键
- **M:N联系**：创建独立的联系表，包含两个实体的主键
- **弱实体**：包含强实体的主键作为外键，与自己的部分键组成主键

### 10. 在统一建模语言UML中，如何进行数据建模的表示？
UML使用类图进行数据建模：
- **类**：表示实体，包含属性和方法
- **属性**：表示实体的特征
- **关联**：表示实体间的联系，可标注多重性
- **继承**：表示泛化关系，用空心三角箭头
- **聚合**：表示整体-部分关系，用空心菱形
- **组合**：表示强聚合关系，用实心菱形

### 11. 请介绍什么是IDEF1x？它的概念建模的主要表达成分包括哪些？
IDEF1x是一种数据建模方法，是ER模型的扩展和细化，用于建立系统信息模型。

**主要表达成分**：
- **实体**：系统中的对象
- **属性**：实体的特征
- **关键字**：唯一标识实体的属性
- **联系**：实体间的关联
- **域**：属性的取值范围
- **约束**：数据的限制条件

### 12. 在IDEF1x中，包括哪些实体的类型？
- **独立实体**：不依赖其他实体存在，有自己的主键
- **从属实体**：依赖其他实体存在，主键包含其他实体的外键
- **分类实体**：表示分类关系的实体
- **关联实体**：表示多对多关系的实体，由多个实体的主键组成

### 13. 在IDEF1x中，包括哪些联系的类型？
- **标识联系**：子实体的主键包含父实体的主键，用实线表示
- **非标识联系**：子实体的主键不包含父实体的主键，用虚线表示
- **分类联系**：表示泛化/特化关系，用带圆圈的线表示
- **非特定联系**：一般的关联关系，不影响实体的标识

**联系的基数**：
- **一对一（1:1）**
- **一对多（1:N）**
- **多对多（M:N）**

**联系的可选性**：
- **必选**：实体必须参与联系
- **可选**：实体可以不参与联系

---

*本文档整理了第2章ER模型的详细答案，涵盖了数据库设计的各个阶段、ER模型的核心概念、扩展表示方法以及IDEF1x建模方法。*
