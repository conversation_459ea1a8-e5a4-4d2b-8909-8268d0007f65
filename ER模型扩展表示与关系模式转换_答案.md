# ER模型扩展表示与关系模式转换

## 一、ER模型扩展表示的实用例子

### 1. 聚集（Aggregation）实例

#### 例子：医院管理系统中的"医生诊治病人"聚集
**场景描述**：
- 医生诊治病人形成一个诊疗记录
- 诊疗记录需要关联到具体的科室进行统计和管理
- 药品供应商需要根据诊疗记录向科室供应药品

**聚集结构**：
```
医生 ——— 诊治 ——— 病人
    \              /
     \            /
      \          /
       诊疗记录聚集
           |
           |
        关联到
           |
           |
         科室
```

**优点**：
- **简化复杂关系**：将三元关系（医生-病人-科室）分解为更易理解的二元关系
- **便于扩展**：可以轻松添加新的实体与诊疗记录聚集建立关系
- **语义清晰**：明确表达了"诊疗记录"作为一个整体概念的含义
- **减少冗余**：避免在多个地方重复定义诊疗相关的属性

### 2. 弱实体（Weak Entity）实例

#### 例子：银行系统中的"交易记录"
**场景描述**：
- 银行账户是强实体，有唯一的账户号
- 交易记录是弱实体，依赖于银行账户存在
- 交易记录的标识需要结合账户号和交易序号

**弱实体结构**：
```
银行账户 ——————— 产生 ——————— 交易记录
(账户号)                      (交易序号, 交易时间, 金额, 类型)
[强实体]                      [弱实体]
```

**标识关系**：
- 强实体：银行账户（主键：账户号）
- 弱实体：交易记录（部分键：交易序号，完整标识：账户号+交易序号）

**优点**：
- **逻辑依赖性**：正确反映了交易记录必须依附于账户的现实关系
- **标识合理性**：交易序号在单个账户内唯一，符合实际业务逻辑
- **数据完整性**：确保不会出现没有对应账户的交易记录
- **存储效率**：避免为每个交易记录生成全局唯一标识符

### 3. 细化/泛化（Specialization/Generalization）实例

#### 例子：电商平台的"商品"分类体系
**场景描述**：
- 所有商品都有共同属性：商品ID、名称、价格、库存
- 不同类型商品有特殊属性：
  - 图书：ISBN、作者、出版社、页数
  - 电子产品：品牌、型号、保修期、功率
  - 服装：尺码、颜色、材质、季节

**泛化/细化结构**：
```
                    商品
                (商品ID, 名称, 价格, 库存)
                      |
            ┌─────────┼─────────┐
            |         |         |
          图书      电子产品    服装
    (ISBN,作者,   (品牌,型号,  (尺码,颜色,
     出版社,页数)  保修期,功率)  材质,季节)
```

**优点**：
- **属性继承**：子类自动继承父类的所有属性，减少重复定义
- **多态支持**：可以统一处理所有商品，也可以针对特定类型进行专门处理
- **扩展性强**：添加新的商品类型时，只需定义其特有属性
- **查询灵活**：既支持对所有商品的统一查询，也支持对特定类型的专门查询
- **维护简便**：公共属性的修改只需在父类中进行

## 二、关系模式到ER图的转换

### 给定关系模式分析

**关系模式**：
- R1(a1, a2, a3) - 主键：a1
- R2(a3, a4, a1) - 主键：a3
- R3(a5, a6) - 主键：a5
- R4(a3, a5, a7) - 主键：a3, a5
- R5(a1, a3, a5, a8) - 主键：a1, a3, a5

### 主外键关系分析

**外键关系识别**：
1. R2中的a1 → R1的a1（外键关系）
2. R4中的a3 → R2的a3（外键关系）
3. R4中的a5 → R3的a5（外键关系）
4. R5中的a1 → R1的a1（外键关系）
5. R5中的a3 → R2的a3（外键关系）
6. R5中的a5 → R3的a5（外键关系）

### 实体和联系识别

**实体识别**：
- **实体E1**：对应R1，主键a1，属性a2
- **实体E2**：对应R2，主键a3，属性a4
- **实体E3**：对应R3，主键a5，属性a6

**联系识别**：
- **联系L1**：E2与E1之间的联系（1:N），对应R2中的外键a1
- **联系L2**：E2与E3之间的联系，通过R4表示（M:N）
- **联系L3**：E1、E2、E3之间的三元联系，对应R5（M:N:P）

### ER图设计

```
     E1                    E2                    E3
  ┌─────────┐           ┌─────────┐           ┌─────────┐
  │   a1    │           │   a3    │           │   a5    │
  │  (PK)   │           │  (PK)   │           │  (PK)   │
  │   a2    │           │   a4    │           │   a6    │
  └─────────┘           └─────────┘           └─────────┘
       │                     │                     │
       │                     │                     │
       │        L1           │         L2          │
       │      (1:N)          │       (M:N)         │
       └─────────────────────┤                     │
                             │                     │
                             └─────────────────────┘
                                       │
                                       │
                                      L3
                                   (M:N:P)
                                   属性:a8
```

### 详细ER图说明

**实体**：
1. **E1实体**：
   - 主键：a1
   - 属性：a2

2. **E2实体**：
   - 主键：a3
   - 属性：a4

3. **E3实体**：
   - 主键：a5
   - 属性：a6

**联系**：
1. **L1联系**（E1与E2）：
   - 类型：一对多（1:N）
   - E1的一个实例可以对应E2的多个实例
   - 在R2中通过外键a1体现

2. **L2联系**（E2与E3）：
   - 类型：多对多（M:N）
   - 对应关系R4
   - 联系属性：a7

3. **L3联系**（E1、E2、E3三元联系）：
   - 类型：多对多对多（M:N:P）
   - 对应关系R5
   - 联系属性：a8

### 转换验证

**从ER图推导关系模式**：
1. **E1** → R1(a1, a2)
2. **E2** → R2(a3, a4, a1) [a1为外键]
3. **E3** → R3(a5, a6)
4. **L2联系** → R4(a3, a5, a7) [a3, a5为外键]
5. **L3联系** → R5(a1, a3, a5, a8) [a1, a3, a5为外键]

这样的转换完全符合给定的关系模式，验证了ER图设计的正确性。

### 设计优势

1. **结构清晰**：明确区分了实体和联系
2. **关系明确**：准确反映了实体间的各种关系类型
3. **扩展性好**：便于后续添加新的实体或联系
4. **维护方便**：修改某个实体不会影响其他实体的定义

## 三、NBA球员转会汇总表的ER图设计

### 数据分析

根据提供的NBA球员转会汇总表，我们可以识别出以下信息：

**表格字段分析**：
- 交易ID：每笔交易的唯一标识
- 转出球队、转入球队：参与交易的两支球队
- 转出球队薪酬总额、转入球队薪酬总额：各队在交易中的薪酬
- 交易球员：参与交易的球员
- 球员工资：球员的薪酬
- 交易时间：交易发生的日期

### 实体识别

1. **球队实体**
   - 属性：球队名称、所在城市、成立时间等

2. **球员实体**
   - 属性：球员姓名、位置、年龄、工资等

3. **交易实体**
   - 属性：交易ID、交易时间、交易类型等

### ER图设计

```
        球队                                    球员
    ┌─────────┐                            ┌─────────────┐
    │ 球队名称 │                            │  球员姓名    │
    │ 所在城市 │                            │  位置       │
    │ 成立时间 │                            │  年龄       │
    └─────────┘                            │  当前工资    │
         │                                 └─────────────┘
         │                                       │
         │                                       │
    ┌────┴────┐                             ┌────┴────┐
    │ 转出交易 │                             │ 参与交易 │
    │ (1:N)   │                             │ (M:N)   │
    └────┬────┘                             └────┬────┘
         │                                       │
         │              交易                      │
         │         ┌─────────────┐                │
         └─────────│   交易ID     │────────────────┘
                   │   交易时间   │
                   │ 转出薪酬总额 │
                   │ 转入薪酬总额 │
                   └─────────────┘
                          │
                          │
                     ┌────┴────┐
                     │ 转入交易 │
                     │ (1:N)   │
                     └────┬────┘
                          │
                      球队
```

### 关系说明

1. **球队-交易关系**：
   - 转出关系：一个球队可以参与多次转出交易（1:N）
   - 转入关系：一个球队可以参与多次转入交易（1:N）

2. **球员-交易关系**：
   - 参与关系：一个球员可以参与多次交易，一次交易可以涉及多个球员（M:N）
   - 关系属性：球员在该交易中的工资

## 四、微信数据库的ER图设计

### 实体分析

根据给定的表结构，识别出以下实体：

1. **信友实体**
   - 主键：信友ID
   - 属性：信友名、昵称、所在区域、手机号

2. **群实体**
   - 主键：群ID
   - 属性：群名称、群类型、创建时间

3. **帖子实体**
   - 主键：帖子ID
   - 属性：帖子内容、发帖时间

4. **短信实体**
   - 主键：短信ID
   - 属性：短信时间、短信内容

### ER图设计

```
                    信友
               ┌─────────────┐
               │   信友ID    │
               │   信友名    │
               │   昵称      │
               │  所在区域   │
               │   手机号    │
               └─────────────┘
                      │
        ┌─────────────┼─────────────┐
        │             │             │
   ┌────┴────┐   ┌────┴────┐   ┌────┴────┐
   │ 通讯录   │   │ 群主关系 │   │ 发帖关系 │
   │ (M:N)   │   │ (1:N)   │   │ (1:N)   │
   └────┬────┘   └────┬────┘   └────┬────┘
        │             │             │
        │             │             │
     信友             群           帖子
                 ┌─────────┐   ┌─────────────┐
                 │  群ID   │   │   帖子ID    │
                 │ 群名称  │   │  帖子内容   │
                 │ 群类型  │   │  发帖时间   │
                 │创建时间 │   └─────────────┘
                 └─────────┘          │
                      │               │
                 ┌────┴────┐     ┌────┴────┐
                 │ 群成员   │     │ 所属群   │
                 │ (M:N)   │     │ (M:1)   │
                 └────┬────┘     └─────────┘
                      │
                    信友

              信友 ────── 发送短信 ────── 信友
                        (M:N)
                         │
                       短信
                  ┌─────────────┐
                  │   短信ID    │
                  │  短信时间   │
                  │  短信内容   │
                  └─────────────┘
```

### 关系详细说明

1. **通讯录关系**（信友-信友，M:N）
   - 关系属性：认识方式、认识时间
   - 表示两个信友之间的好友关系

2. **群主关系**（信友-群，1:N）
   - 一个信友可以创建多个群
   - 一个群只有一个群主

3. **群成员关系**（信友-群，M:N）
   - 关系属性：加入时间、引介人ID、信友群内昵称
   - 一个信友可以加入多个群，一个群可以有多个成员

4. **发帖关系**（信友-帖子，1:N）
   - 一个信友可以发多个帖子
   - 一个帖子只有一个发帖人

5. **所属群关系**（帖子-群，M:1）
   - 一个帖子属于一个群
   - 一个群可以有多个帖子

6. **发送短信关系**（信友-信友，M:N）
   - 通过短信实体实现
   - 关系属性包含在短信实体中

## 五、论文评审数据库的ER图设计

### 需求分析

根据描述，论文评审系统包含以下核心业务：
- 论文投稿和作者管理
- 审稿人分配和主题管理
- 评审打分和意见反馈

### 实体识别

1. **论文实体**
   - 主键：论文ID
   - 属性：标题、摘要

2. **作者实体**
   - 主键：作者ID
   - 属性：姓名

3. **审稿人实体**
   - 主键：审稿人ID
   - 属性：email

4. **主题实体**
   - 主键：主题ID
   - 属性：名称

5. **评审实体**
   - 主键：评审ID
   - 属性：可读性得分、创新性得分、相关性得分、书面意见

### ER图设计

```
                        作者
                   ┌─────────────┐
                   │   作者ID    │
                   │    姓名     │
                   └─────────────┘
                          │
                     ┌────┴────┐
                     │ 论文作者 │
                     │ (M:N)   │
                     └────┬────┘
                          │
                        论文
                   ┌─────────────┐
                   │   论文ID    │
                   │    标题     │
                   │    摘要     │
                   └─────────────┘
                          │
        ┌─────────────────┼─────────────────┐
        │                 │                 │
   ┌────┴────┐       ┌────┴────┐       ┌────┴────┐
   │ 通讯作者 │       │ 所属主题 │       │ 论文评审 │
   │ (M:1)   │       │ (M:1)   │       │ (M:N)   │
   └────┬────┘       └────┬────┘       └────┬────┘
        │                 │                 │
      作者              主题              评审
                   ┌─────────────┐   ┌─────────────────┐
                   │   主题ID    │   │    评审ID       │
                   │    名称     │   │  可读性得分     │
                   └─────────────┘   │  创新性得分     │
                          │         │  相关性得分     │
                     ┌────┴────┐    │   书面意见      │
                     │ 主题主持 │    └─────────────────┘
                     │ (1:1)   │             │
                     └────┬────┘        ┌────┴────┐
                          │             │ 审稿人评审│
                       审稿人            │ (1:N)   │
                   ┌─────────────┐       └────┬────┘
                   │  审稿人ID   │            │
                   │   email     │          审稿人
                   └─────────────┘
                          │
                     ┌────┴────┐
                     │ 关心主题 │
                     │ (M:N)   │
                     └────┬────┘
                          │
                        主题
```

### 关系详细说明

1. **论文作者关系**（论文-作者，M:N）
   - 一篇论文可以有多个作者
   - 一个作者可以投稿多篇论文

2. **通讯作者关系**（论文-作者，M:1）
   - 每篇论文有且仅有一个通讯作者
   - 一个作者可以是多篇论文的通讯作者

3. **所属主题关系**（论文-主题，M:1）
   - 每篇论文属于一个主题
   - 一个主题可以包含多篇论文

4. **主题主持关系**（主题-审稿人，1:1）
   - 每个主题由一位审稿人主持
   - 一个审稿人可以主持一个主题

5. **关心主题关系**（审稿人-主题，M:N）
   - 一个审稿人可以关心多个主题
   - 一个主题可以被多个审稿人关心

6. **论文评审关系**（论文-评审，1:N）
   - 每篇论文有多个评审记录（4个）
   - 每个评审记录对应一篇论文

7. **审稿人评审关系**（审稿人-评审，1:N）
   - 每个审稿人可以进行多次评审
   - 每次评审由一个审稿人完成

### 业务规则实现

1. **每篇论文分配给4位审稿人**：
   - 通过论文评审关系实现，确保每篇论文有4条评审记录

2. **评分标准**：
   - 在评审实体中设置可读性、创新性、相关性三个得分属性
   - 每个得分范围为1-10分

3. **书面意见反馈**：
   - 评审实体包含书面意见属性
   - 通过通讯作者关系，意见可以反馈给对应的通讯作者

### 数据完整性约束

1. **实体完整性**：
   - 所有实体都有明确的主键

2. **参照完整性**：
   - 所有外键关系都有明确的参照约束

3. **用户定义完整性**：
   - 评分范围：1-10分
   - 每篇论文必须有且仅有4个评审
   - 每篇论文必须有且仅有1个通讯作者
   - 每个主题必须有且仅有1个主持人

## 六、航班管理系统的业务关系分析与ER图设计

### 业务关系分析

#### 1. 机场相关关系
- **机场基本信息**：机场代码、名称、城市、国家、时区
- **机场设施**：跑道数量、航站楼数量、停机位数量
- **机场运营**：起降时间限制、天气条件影响

#### 2. 航线相关关系
- **航线定义**：起始机场、目的地机场、航线代码
- **航线属性**：飞行距离、预计飞行时间、航线类型（国内/国际）
- **航线运营**：航空公司运营权限、季节性调整

#### 3. 飞机相关关系
- **飞机基本信息**：机型、注册号、制造商、制造年份
- **飞机性能**：载客量、航程、巡航速度、燃油容量
- **飞机状态**：当前位置、维护状态、适航证有效期
- **飞机归属**：所属航空公司、租赁状态

#### 4. 飞行员相关关系
- **飞行员基本信息**：执照号、姓名、年龄、经验年限
- **飞行员资质**：机型资格、飞行等级、体检有效期
- **飞行员状态**：当前状态（可用/休息/飞行中）、累计飞行时间
- **飞行员归属**：所属航空公司、基地机场

#### 5. 机组相关关系
- **机组构成**：机长、副驾驶、乘务长、乘务员
- **机组配置**：不同机型的机组人员要求
- **机组调度**：值班安排、休息时间管理
- **机组资质**：团队协作训练、应急处理能力

#### 6. 航班相关关系
- **航班基本信息**：航班号、日期、起飞时间、到达时间
- **航班执行**：实际起飞时间、实际到达时间、延误原因
- **航班资源**：执飞飞机、执飞机组、使用航线
- **航班状态**：计划中/执行中/已完成/取消

#### 7. 复杂业务关系
- **机型适配**：飞行员需要特定机型的飞行资格
- **机场限制**：某些机型不能在特定机场起降
- **航线授权**：航空公司需要航线运营许可
- **机组轮换**：飞行员的工作时间和休息时间限制
- **维护计划**：飞机定期维护对航班安排的影响

### 实体识别与属性定义

#### 1. 机场实体
```
机场 (Airport)
- 机场代码 (airport_code) [PK]
- 机场名称 (airport_name)
- 城市 (city)
- 国家 (country)
- 时区 (timezone)
- 跑道数量 (runway_count)
- 纬度 (latitude)
- 经度 (longitude)
```

#### 2. 航线实体
```
航线 (Route)
- 航线ID (route_id) [PK]
- 起始机场代码 (origin_airport) [FK]
- 目的地机场代码 (destination_airport) [FK]
- 飞行距离 (distance)
- 预计飞行时间 (estimated_duration)
- 航线类型 (route_type) [国内/国际]
```

#### 3. 飞机实体
```
飞机 (Aircraft)
- 注册号 (registration_number) [PK]
- 机型 (aircraft_type)
- 制造商 (manufacturer)
- 制造年份 (manufacture_year)
- 载客量 (passenger_capacity)
- 航程 (range)
- 当前机场 (current_airport) [FK]
- 状态 (status) [可用/维护/停飞]
```

#### 4. 飞行员实体
```
飞行员 (Pilot)
- 执照号 (license_number) [PK]
- 姓名 (name)
- 出生日期 (birth_date)
- 飞行等级 (pilot_rank) [机长/副驾驶]
- 累计飞行时间 (total_flight_hours)
- 体检有效期 (medical_expiry)
- 基地机场 (base_airport) [FK]
```

#### 5. 机组实体
```
机组 (Crew)
- 机组ID (crew_id) [PK]
- 机组类型 (crew_type) [驾驶员/乘务员]
- 创建日期 (created_date)
- 状态 (status) [可用/执行任务/休息]
```

#### 6. 航班实体
```
航班 (Flight)
- 航班ID (flight_id) [PK]
- 航班号 (flight_number)
- 航线ID (route_id) [FK]
- 飞机注册号 (aircraft_registration) [FK]
- 计划起飞时间 (scheduled_departure)
- 计划到达时间 (scheduled_arrival)
- 实际起飞时间 (actual_departure)
- 实际到达时间 (actual_arrival)
- 航班状态 (flight_status)
- 航班日期 (flight_date)
```

### 关系识别与基数约束

#### 1. 机场-航线关系
- **起始机场关系** (1:N)：一个机场可以是多条航线的起点
- **目的地机场关系** (1:N)：一个机场可以是多条航线的终点

#### 2. 飞机-航班关系
- **执飞关系** (1:N)：一架飞机可以执飞多个航班，一个航班只能由一架飞机执飞

#### 3. 飞行员-机组关系
- **机组成员关系** (M:N)：一个飞行员可以参与多个机组，一个机组包含多个飞行员
- 关系属性：职务（机长/副驾驶）、加入时间

#### 4. 机组-航班关系
- **执飞关系** (1:N)：一个机组可以执飞多个航班，一个航班由一个机组执飞

#### 5. 飞行员-机型关系
- **飞行资格关系** (M:N)：一个飞行员可以有多个机型资格，一个机型可以被多个飞行员驾驶
- 关系属性：资格获得日期、资格有效期

#### 6. 航空公司相关关系
- **飞机归属关系** (M:1)：多架飞机属于一个航空公司
- **飞行员雇佣关系** (M:1)：多个飞行员受雇于一个航空公司
- **航线运营关系** (M:N)：一个航空公司可以运营多条航线，一条航线可以被多个航空公司运营

### 航班管理系统ER图

```
                    航空公司
               ┌─────────────────┐
               │   公司代码      │
               │   公司名称      │
               │   总部城市      │
               └─────────────────┘
                        │
        ┌───────────────┼───────────────┐
        │               │               │
   ┌────┴────┐     ┌────┴────┐     ┌────┴────┐
   │ 拥有飞机 │     │ 雇佣飞行员│     │ 运营航线 │
   │ (1:N)   │     │ (1:N)   │     │ (M:N)   │
   └────┬────┘     └────┬────┘     └────┬────┘
        │               │               │
      飞机            飞行员           航线
 ┌─────────────┐  ┌─────────────┐  ┌─────────────┐
 │  注册号     │  │  执照号     │  │  航线ID     │
 │  机型       │  │  姓名       │  │  起始机场   │
 │  载客量     │  │  飞行等级   │  │  目的地机场 │
 │  当前状态   │  │  累计时间   │  │  飞行距离   │
 └─────────────┘  └─────────────┘  └─────────────┘
        │               │               │
   ┌────┴────┐     ┌────┴────┐     ┌────┴────┐
   │ 执飞航班 │     │ 机组成员 │     │ 航线机场 │
   │ (1:N)   │     │ (M:N)   │     │ (M:1)   │
   └────┬────┘     └────┬────┘     └────┬────┘
        │               │               │
      航班            机组             机场
 ┌─────────────┐  ┌─────────────┐  ┌─────────────┐
 │  航班ID     │  │  机组ID     │  │  机场代码   │
 │  航班号     │  │  机组类型   │  │  机场名称   │
 │  计划起飞   │  │  创建日期   │  │  城市       │
 │  实际起飞   │  │  状态       │  │  国家       │
 │  航班状态   │  └─────────────┘  │  时区       │
 └─────────────┘          │       └─────────────┘
        │            ┌────┴────┐
   ┌────┴────┐       │ 执飞航班 │
   │ 使用航线 │       │ (1:N)   │
   │ (M:1)   │       └─────────┘
   └─────────┘

        飞行员 ────── 机型资格 ────── 机型
                     (M:N)
                 [资格获得日期]
                 [资格有效期]
```

### 业务规则与约束

#### 1. 时间约束
- 飞行员连续工作时间不超过规定限制
- 航班起飞时间必须在机场运营时间内
- 飞机维护期间不能安排航班

#### 2. 资格约束
- 飞行员必须具备对应机型的飞行资格
- 机长必须有足够的飞行经验
- 机组人员数量必须满足机型要求

#### 3. 物理约束
- 飞机不能同时出现在两个地方
- 航班的起始机场必须是飞机当前位置
- 机场跑道长度必须满足机型要求

## 七、知乎网站数据库设计

### 业务需求分析

知乎作为问答社区平台，核心功能包括：
- 用户注册和个人资料管理
- 问题发布和回答
- 内容点赞、收藏、评论
- 关注用户和话题
- 专栏文章发布
- 私信和通知系统

### 数据库表结构设计

#### 1. 用户相关表

```sql
-- 用户基本信息表
CREATE TABLE users (
    user_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    nickname VARCHAR(100),
    avatar_url VARCHAR(500),
    bio TEXT,
    location VARCHAR(100),
    website VARCHAR(200),
    gender ENUM('male', 'female', 'other'),
    birth_date DATE,
    registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    is_verified BOOLEAN DEFAULT FALSE,
    follower_count INT DEFAULT 0,
    following_count INT DEFAULT 0,
    status ENUM('active', 'suspended', 'deleted') DEFAULT 'active'
);

-- 用户关注关系表
CREATE TABLE user_follows (
    follower_id BIGINT,
    following_id BIGINT,
    follow_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (follower_id, following_id),
    FOREIGN KEY (follower_id) REFERENCES users(user_id),
    FOREIGN KEY (following_id) REFERENCES users(user_id)
);
```

#### 2. 话题相关表

```sql
-- 话题表
CREATE TABLE topics (
    topic_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    topic_name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    avatar_url VARCHAR(500),
    follower_count INT DEFAULT 0,
    question_count INT DEFAULT 0,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('active', 'inactive') DEFAULT 'active'
);

-- 用户关注话题表
CREATE TABLE user_topic_follows (
    user_id BIGINT,
    topic_id BIGINT,
    follow_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, topic_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (topic_id) REFERENCES topics(topic_id)
);
```

#### 3. 问题相关表

```sql
-- 问题表
CREATE TABLE questions (
    question_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(500) NOT NULL,
    content TEXT,
    author_id BIGINT NOT NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    view_count INT DEFAULT 0,
    answer_count INT DEFAULT 0,
    follower_count INT DEFAULT 0,
    is_anonymous BOOLEAN DEFAULT FALSE,
    status ENUM('active', 'closed', 'deleted') DEFAULT 'active',
    FOREIGN KEY (author_id) REFERENCES users(user_id)
);

-- 问题话题关联表
CREATE TABLE question_topics (
    question_id BIGINT,
    topic_id BIGINT,
    PRIMARY KEY (question_id, topic_id),
    FOREIGN KEY (question_id) REFERENCES questions(question_id),
    FOREIGN KEY (topic_id) REFERENCES topics(topic_id)
);

-- 用户关注问题表
CREATE TABLE question_follows (
    user_id BIGINT,
    question_id BIGINT,
    follow_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, question_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (question_id) REFERENCES questions(question_id)
);
```

#### 4. 回答相关表

```sql
-- 回答表
CREATE TABLE answers (
    answer_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    question_id BIGINT NOT NULL,
    author_id BIGINT NOT NULL,
    content TEXT NOT NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    upvote_count INT DEFAULT 0,
    downvote_count INT DEFAULT 0,
    comment_count INT DEFAULT 0,
    is_anonymous BOOLEAN DEFAULT FALSE,
    status ENUM('active', 'deleted') DEFAULT 'active',
    FOREIGN KEY (question_id) REFERENCES questions(question_id),
    FOREIGN KEY (author_id) REFERENCES users(user_id)
);

-- 回答投票表
CREATE TABLE answer_votes (
    user_id BIGINT,
    answer_id BIGINT,
    vote_type ENUM('upvote', 'downvote') NOT NULL,
    vote_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, answer_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (answer_id) REFERENCES answers(answer_id)
);
```

#### 5. 评论相关表

```sql
-- 评论表
CREATE TABLE comments (
    comment_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    content TEXT NOT NULL,
    author_id BIGINT NOT NULL,
    target_type ENUM('answer', 'article', 'comment') NOT NULL,
    target_id BIGINT NOT NULL,
    parent_comment_id BIGINT NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    upvote_count INT DEFAULT 0,
    reply_count INT DEFAULT 0,
    status ENUM('active', 'deleted') DEFAULT 'active',
    FOREIGN KEY (author_id) REFERENCES users(user_id),
    FOREIGN KEY (parent_comment_id) REFERENCES comments(comment_id)
);
```

#### 6. 专栏和文章表

```sql
-- 专栏表
CREATE TABLE columns (
    column_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    author_id BIGINT NOT NULL,
    avatar_url VARCHAR(500),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    follower_count INT DEFAULT 0,
    article_count INT DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    FOREIGN KEY (author_id) REFERENCES users(user_id)
);

-- 文章表
CREATE TABLE articles (
    article_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(500) NOT NULL,
    content TEXT NOT NULL,
    summary TEXT,
    author_id BIGINT NOT NULL,
    column_id BIGINT,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    view_count INT DEFAULT 0,
    upvote_count INT DEFAULT 0,
    comment_count INT DEFAULT 0,
    status ENUM('active', 'deleted') DEFAULT 'active',
    FOREIGN KEY (author_id) REFERENCES users(user_id),
    FOREIGN KEY (column_id) REFERENCES columns(column_id)
);
```

#### 7. 收藏和通知表

```sql
-- 收藏表
CREATE TABLE collections (
    user_id BIGINT,
    target_type ENUM('question', 'answer', 'article') NOT NULL,
    target_id BIGINT NOT NULL,
    collection_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, target_type, target_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

-- 通知表
CREATE TABLE notifications (
    notification_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    type ENUM('follow', 'answer', 'comment', 'upvote', 'mention') NOT NULL,
    content TEXT,
    related_id BIGINT,
    is_read BOOLEAN DEFAULT FALSE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);
```

## 八、电子商务网站数据库设计

### 业务需求分析

电商平台核心功能包括：
- 用户注册和个人信息管理
- 商品展示和分类管理
- 购物车和订单处理
- 支付和物流管理
- 商家入驻和店铺管理
- 评价和客服系统

### 数据库表结构设计

#### 1. 用户相关表

```sql
-- 用户表
CREATE TABLE users (
    user_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    real_name VARCHAR(100),
    gender ENUM('male', 'female', 'other'),
    birth_date DATE,
    registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    status ENUM('active', 'suspended', 'deleted') DEFAULT 'active',
    user_type ENUM('customer', 'merchant', 'admin') DEFAULT 'customer'
);

-- 用户地址表
CREATE TABLE user_addresses (
    address_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    recipient_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    province VARCHAR(50) NOT NULL,
    city VARCHAR(50) NOT NULL,
    district VARCHAR(50) NOT NULL,
    detailed_address VARCHAR(500) NOT NULL,
    postal_code VARCHAR(10),
    is_default BOOLEAN DEFAULT FALSE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);
```

#### 2. 商家和店铺表

```sql
-- 商家表
CREATE TABLE merchants (
    merchant_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    company_name VARCHAR(200) NOT NULL,
    business_license VARCHAR(100) UNIQUE NOT NULL,
    legal_person VARCHAR(100) NOT NULL,
    contact_phone VARCHAR(20) NOT NULL,
    contact_email VARCHAR(100) NOT NULL,
    registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('pending', 'approved', 'rejected', 'suspended') DEFAULT 'pending',
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

-- 店铺表
CREATE TABLE shops (
    shop_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    merchant_id BIGINT NOT NULL,
    shop_name VARCHAR(200) NOT NULL,
    shop_description TEXT,
    logo_url VARCHAR(500),
    banner_url VARCHAR(500),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    follower_count INT DEFAULT 0,
    product_count INT DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 5.00,
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    FOREIGN KEY (merchant_id) REFERENCES merchants(merchant_id)
);
```

#### 3. 商品相关表

```sql
-- 商品分类表
CREATE TABLE categories (
    category_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    category_name VARCHAR(100) NOT NULL,
    parent_category_id BIGINT NULL,
    level INT NOT NULL DEFAULT 1,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (parent_category_id) REFERENCES categories(category_id)
);

-- 商品表
CREATE TABLE products (
    product_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    shop_id BIGINT NOT NULL,
    category_id BIGINT NOT NULL,
    product_name VARCHAR(500) NOT NULL,
    description TEXT,
    brand VARCHAR(100),
    model VARCHAR(100),
    price DECIMAL(10,2) NOT NULL,
    original_price DECIMAL(10,2),
    stock_quantity INT NOT NULL DEFAULT 0,
    sales_count INT DEFAULT 0,
    view_count INT DEFAULT 0,
    weight DECIMAL(8,3),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('active', 'inactive', 'out_of_stock', 'deleted') DEFAULT 'active',
    FOREIGN KEY (shop_id) REFERENCES shops(shop_id),
    FOREIGN KEY (category_id) REFERENCES categories(category_id)
);

-- 商品图片表
CREATE TABLE product_images (
    image_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    product_id BIGINT NOT NULL,
    image_url VARCHAR(500) NOT NULL,
    is_main BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    FOREIGN KEY (product_id) REFERENCES products(product_id)
);

-- 商品规格表
CREATE TABLE product_specs (
    spec_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    product_id BIGINT NOT NULL,
    spec_name VARCHAR(100) NOT NULL,
    spec_value VARCHAR(200) NOT NULL,
    price_adjustment DECIMAL(10,2) DEFAULT 0,
    stock_quantity INT NOT NULL DEFAULT 0,
    sku_code VARCHAR(100) UNIQUE,
    FOREIGN KEY (product_id) REFERENCES products(product_id)
);
```

#### 4. 购物车相关表

```sql
-- 购物车表
CREATE TABLE shopping_cart (
    cart_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    product_id BIGINT NOT NULL,
    spec_id BIGINT,
    quantity INT NOT NULL DEFAULT 1,
    added_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (product_id) REFERENCES products(product_id),
    FOREIGN KEY (spec_id) REFERENCES product_specs(spec_id)
);
```

#### 5. 订单相关表

```sql
-- 订单表
CREATE TABLE orders (
    order_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    user_id BIGINT NOT NULL,
    shop_id BIGINT NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    shipping_fee DECIMAL(10,2) DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    final_amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('alipay', 'wechat', 'credit_card', 'bank_transfer') NOT NULL,
    order_status ENUM('pending', 'paid', 'shipped', 'delivered', 'completed', 'cancelled', 'refunded') DEFAULT 'pending',
    shipping_address_id BIGINT NOT NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    paid_date TIMESTAMP NULL,
    shipped_date TIMESTAMP NULL,
    delivered_date TIMESTAMP NULL,
    notes TEXT,
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (shop_id) REFERENCES shops(shop_id),
    FOREIGN KEY (shipping_address_id) REFERENCES user_addresses(address_id)
);

-- 订单商品表
CREATE TABLE order_items (
    item_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_id BIGINT NOT NULL,
    product_id BIGINT NOT NULL,
    spec_id BIGINT,
    product_name VARCHAR(500) NOT NULL,
    spec_info VARCHAR(200),
    unit_price DECIMAL(10,2) NOT NULL,
    quantity INT NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    FOREIGN KEY (order_id) REFERENCES orders(order_id),
    FOREIGN KEY (product_id) REFERENCES products(product_id),
    FOREIGN KEY (spec_id) REFERENCES product_specs(spec_id)
);
```

#### 6. 支付相关表

```sql
-- 支付记录表
CREATE TABLE payments (
    payment_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_id BIGINT NOT NULL,
    payment_number VARCHAR(50) UNIQUE NOT NULL,
    payment_method ENUM('alipay', 'wechat', 'credit_card', 'bank_transfer') NOT NULL,
    payment_amount DECIMAL(10,2) NOT NULL,
    payment_status ENUM('pending', 'success', 'failed', 'cancelled') DEFAULT 'pending',
    third_party_transaction_id VARCHAR(100),
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_date TIMESTAMP NULL,
    FOREIGN KEY (order_id) REFERENCES orders(order_id)
);
```

#### 7. 物流相关表

```sql
-- 物流公司表
CREATE TABLE logistics_companies (
    company_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    company_name VARCHAR(100) NOT NULL,
    company_code VARCHAR(20) UNIQUE NOT NULL,
    contact_phone VARCHAR(20),
    website VARCHAR(200),
    is_active BOOLEAN DEFAULT TRUE
);

-- 物流信息表
CREATE TABLE shipping_info (
    shipping_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_id BIGINT NOT NULL,
    logistics_company_id BIGINT NOT NULL,
    tracking_number VARCHAR(100) NOT NULL,
    shipping_status ENUM('preparing', 'shipped', 'in_transit', 'delivered', 'exception') DEFAULT 'preparing',
    shipped_date TIMESTAMP NULL,
    estimated_delivery_date TIMESTAMP NULL,
    actual_delivery_date TIMESTAMP NULL,
    FOREIGN KEY (order_id) REFERENCES orders(order_id),
    FOREIGN KEY (logistics_company_id) REFERENCES logistics_companies(company_id)
);

-- 物流跟踪表
CREATE TABLE shipping_tracking (
    tracking_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    shipping_id BIGINT NOT NULL,
    location VARCHAR(200),
    description TEXT NOT NULL,
    tracking_time TIMESTAMP NOT NULL,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (shipping_id) REFERENCES shipping_info(shipping_id)
);
```

#### 8. 评价相关表

```sql
-- 商品评价表
CREATE TABLE product_reviews (
    review_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_id BIGINT NOT NULL,
    product_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    rating INT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    content TEXT,
    is_anonymous BOOLEAN DEFAULT FALSE,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    helpful_count INT DEFAULT 0,
    status ENUM('active', 'hidden', 'deleted') DEFAULT 'active',
    FOREIGN KEY (order_id) REFERENCES orders(order_id),
    FOREIGN KEY (product_id) REFERENCES products(product_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id)
);

-- 评价图片表
CREATE TABLE review_images (
    image_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    review_id BIGINT NOT NULL,
    image_url VARCHAR(500) NOT NULL,
    FOREIGN KEY (review_id) REFERENCES product_reviews(review_id)
);
```

#### 9. 优惠券和促销表

```sql
-- 优惠券表
CREATE TABLE coupons (
    coupon_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    coupon_name VARCHAR(200) NOT NULL,
    coupon_type ENUM('fixed_amount', 'percentage', 'free_shipping') NOT NULL,
    discount_value DECIMAL(10,2) NOT NULL,
    min_order_amount DECIMAL(10,2) DEFAULT 0,
    max_discount_amount DECIMAL(10,2),
    total_quantity INT NOT NULL,
    used_quantity INT DEFAULT 0,
    start_date TIMESTAMP NOT NULL,
    end_date TIMESTAMP NOT NULL,
    applicable_products TEXT, -- JSON格式存储适用商品ID
    status ENUM('active', 'inactive', 'expired') DEFAULT 'active',
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户优惠券表
CREATE TABLE user_coupons (
    user_coupon_id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    coupon_id BIGINT NOT NULL,
    order_id BIGINT NULL,
    received_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    used_date TIMESTAMP NULL,
    status ENUM('unused', 'used', 'expired') DEFAULT 'unused',
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    FOREIGN KEY (coupon_id) REFERENCES coupons(coupon_id),
    FOREIGN KEY (order_id) REFERENCES orders(order_id)
);
```

### 电商系统ER图关系总结

该电商系统数据库设计涵盖了：

1. **用户管理**：用户注册、地址管理、商家认证
2. **商品管理**：分类体系、商品信息、规格管理、库存管理
3. **交易流程**：购物车、订单生成、支付处理、物流跟踪
4. **评价系统**：商品评价、图片上传、评价统计
5. **营销系统**：优惠券发放、使用跟踪、促销活动

核心业务流程：
- 用户浏览商品 → 加入购物车 → 生成订单 → 支付 → 发货 → 收货 → 评价

---

*本文档详细分析了航班管理系统、知乎问答平台和电子商务网站的数据库设计，涵盖了实体关系建模、表结构设计和业务流程实现。*
```
```
