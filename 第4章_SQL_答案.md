# 第4章 SQL - 答案

## SQL基础概念

### 1. 什么是SQL？SQL的主要功能是什么？
SQL（Structured Query Language，结构化查询语言）是一种专门用于管理关系数据库的标准化语言。

**主要功能**：
- **数据定义**：创建、修改、删除数据库对象（DDL）
- **数据操作**：查询、插入、更新、删除数据（DML）
- **数据控制**：权限管理和事务控制（DCL）
- **数据查询**：复杂的数据检索和分析

### 2. SQL语言的特点有哪些？
- **非过程化语言**：只需说明做什么，不需说明怎么做
- **统一的语法结构**：DDL、DML、DCL使用统一的语法
- **高度标准化**：有国际标准，可移植性强
- **面向集合的操作**：一次操作可处理多条记录
- **简洁易学**：语法接近自然语言

### 3. SQL的数据类型有哪些？
**数值类型**：
- INTEGER/INT：整数
- DECIMAL/NUMERIC：定点数
- FLOAT/REAL：浮点数

**字符类型**：
- CHAR(n)：定长字符串
- VARCHAR(n)：变长字符串
- TEXT：长文本

**日期时间类型**：
- DATE：日期
- TIME：时间
- TIMESTAMP：时间戳

## 数据定义语言（DDL）

### 4. 如何创建数据库和表？
```sql
-- 创建数据库
CREATE DATABASE 数据库名;

-- 创建表
CREATE TABLE 表名 (
    列名1 数据类型 [约束],
    列名2 数据类型 [约束],
    ...
    [表级约束]
);
```

### 5. 常见的完整性约束有哪些？
- **主键约束（PRIMARY KEY）**：唯一标识表中的每一行
- **外键约束（FOREIGN KEY）**：维护表间的参照完整性
- **唯一约束（UNIQUE）**：确保列值的唯一性
- **非空约束（NOT NULL）**：确保列不能为空
- **检查约束（CHECK）**：确保列值满足特定条件

### 6. 如何修改表结构？
```sql
-- 添加列
ALTER TABLE 表名 ADD 列名 数据类型;

-- 删除列
ALTER TABLE 表名 DROP COLUMN 列名;

-- 修改列
ALTER TABLE 表名 ALTER COLUMN 列名 数据类型;

-- 添加约束
ALTER TABLE 表名 ADD CONSTRAINT 约束名 约束类型;
```

### 7. 如何删除数据库对象？
```sql
-- 删除表
DROP TABLE 表名;

-- 删除数据库
DROP DATABASE 数据库名;

-- 删除索引
DROP INDEX 索引名;
```

## 数据操作语言（DML）

### 8. 如何插入数据？
```sql
-- 插入单行数据
INSERT INTO 表名 (列1, 列2, ...) VALUES (值1, 值2, ...);

-- 插入多行数据
INSERT INTO 表名 (列1, 列2, ...) VALUES 
    (值1, 值2, ...),
    (值1, 值2, ...);

-- 从其他表插入数据
INSERT INTO 表名 SELECT ... FROM 其他表 WHERE ...;
```

### 9. 如何更新数据？
```sql
UPDATE 表名 
SET 列1 = 值1, 列2 = 值2, ...
WHERE 条件;
```

### 10. 如何删除数据？
```sql
-- 删除满足条件的行
DELETE FROM 表名 WHERE 条件;

-- 删除所有行
DELETE FROM 表名;

-- 截断表（更快的删除所有行）
TRUNCATE TABLE 表名;
```

## 数据查询语言（DQL）

### 11. SELECT语句的基本语法是什么？
```sql
SELECT [DISTINCT] 列名列表
FROM 表名列表
[WHERE 条件]
[GROUP BY 列名列表]
[HAVING 条件]
[ORDER BY 列名列表 [ASC|DESC]]
[LIMIT 数量];
```

### 12. 常用的查询条件有哪些？
- **比较运算符**：=, <>, !=, <, >, <=, >=
- **逻辑运算符**：AND, OR, NOT
- **范围查询**：BETWEEN...AND, IN, NOT IN
- **模式匹配**：LIKE, NOT LIKE（使用%和_通配符）
- **空值查询**：IS NULL, IS NOT NULL

### 13. 如何进行连接查询？
```sql
-- 内连接
SELECT * FROM 表1 INNER JOIN 表2 ON 连接条件;

-- 左外连接
SELECT * FROM 表1 LEFT JOIN 表2 ON 连接条件;

-- 右外连接
SELECT * FROM 表1 RIGHT JOIN 表2 ON 连接条件;

-- 全外连接
SELECT * FROM 表1 FULL OUTER JOIN 表2 ON 连接条件;
```

### 14. 常用的聚合函数有哪些？
- **COUNT()**：计数
- **SUM()**：求和
- **AVG()**：平均值
- **MAX()**：最大值
- **MIN()**：最小值

### 15. 如何使用GROUP BY和HAVING？
```sql
SELECT 列名, 聚合函数(列名)
FROM 表名
WHERE 条件
GROUP BY 列名
HAVING 聚合函数条件;
```

### 16. 什么是子查询？有哪些类型？
子查询是嵌套在其他SQL语句中的SELECT语句。

**类型**：
- **标量子查询**：返回单个值
- **行子查询**：返回单行多列
- **表子查询**：返回多行多列
- **相关子查询**：内层查询依赖外层查询
- **非相关子查询**：内层查询独立于外层查询

### 17. 如何使用EXISTS和NOT EXISTS？
```sql
-- EXISTS
SELECT * FROM 表1 WHERE EXISTS (SELECT 1 FROM 表2 WHERE 条件);

-- NOT EXISTS
SELECT * FROM 表1 WHERE NOT EXISTS (SELECT 1 FROM 表2 WHERE 条件);
```

## 视图

### 18. 什么是视图？视图的作用是什么？
视图是基于一个或多个表的虚拟表，它本身不存储数据，而是存储查询定义。

**作用**：
- **简化复杂查询**：将复杂的查询封装为视图
- **数据安全**：隐藏敏感数据，只暴露需要的列
- **逻辑独立性**：应用程序与基表结构变化隔离
- **数据一致性**：提供统一的数据访问接口

### 19. 如何创建和使用视图？
```sql
-- 创建视图
CREATE VIEW 视图名 AS
SELECT 列名列表
FROM 表名
WHERE 条件;

-- 使用视图
SELECT * FROM 视图名;

-- 删除视图
DROP VIEW 视图名;
```

## 索引

### 20. 什么是索引？索引的作用和类型有哪些？
索引是数据库中用于快速定位数据的数据结构。

**作用**：
- 提高查询速度
- 加速连接操作
- 加速排序和分组

**类型**：
- **聚集索引**：数据按索引顺序物理存储
- **非聚集索引**：索引与数据分离存储
- **唯一索引**：确保索引列值的唯一性
- **复合索引**：基于多个列的索引

### 21. 如何创建和删除索引？
```sql
-- 创建索引
CREATE INDEX 索引名 ON 表名(列名);

-- 创建唯一索引
CREATE UNIQUE INDEX 索引名 ON 表名(列名);

-- 删除索引
DROP INDEX 索引名;
```

---

*本文档整理了第4章SQL的主要知识点和常见问题答案。如需补充具体问题，请提供详细的问题列表。*
