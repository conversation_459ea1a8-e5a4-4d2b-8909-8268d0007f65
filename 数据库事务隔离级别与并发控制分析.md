# 数据库事务隔离级别与并发控制分析

## 问题一：事务隔离级别分析

### 初始状态
- 关系：Employee(ID, salary)
- 初始数据：(A, 20), (B, 30)
- 初始总和：50

### 事务定义

**T1事务**：
```sql
begin transaction;
insert into Employee values ('C', 30);
set salary = salary + 10 where ID = 'A';
commit;
```

**T2事务**：
```sql
begin transaction;
select sum(salary) as sal1 from Employee;
select sum(salary) as sal2 from Employee;
commit;
```

### T1执行后的最终状态
- 数据：(A, 30), (B, 30), (C, 30)
- 最终总和：90

## 隔离级别分析表格

| T2隔离级别 | T1: Repeatable read | T1: Serializable |
|------------|-------------------|------------------|
| **Read uncommitted** | (50,90) 或 (60,90) 或 (80,90) 或 (90,90) | (50,90) 或 (90,90) |
| **Read committed** | (50,90) 或 (60,90) 或 (80,90) 或 (90,90) | (50,90) 或 (90,90) |
| **Repeatable read** | (50,50) 或 (90,90) | (50,50) 或 (90,90) |
| **Serializable** | (50,50) 或 (90,90) | (50,50) 或 (90,90) |

### 详细分析

#### T2: Read Uncommitted

**T1: Repeatable Read**
- T2可以读取T1未提交的数据
- sal1可能读到：50（T1未开始）、60（插入C后）、80（插入C+更新A后）
- sal2总是读到90（T1已提交）
- **可能结果**：(50,90), (60,90), (80,90), (90,90)

**T1: Serializable**
- T1的Serializable级别会阻止T2的并发读取
- T2要么在T1之前执行，要么在T1之后执行
- **可能结果**：(50,90), (90,90)

#### T2: Read Committed

**T1: Repeatable Read**
- T2只能读取已提交的数据
- sal1读到50（T1未提交时）或90（T1已提交时）
- sal2读到90（T1必定已提交）
- 但由于T2不保证可重复读，两次读取可能不一致
- **可能结果**：(50,90), (60,90), (80,90), (90,90)

**T1: Serializable**
- 串行化执行
- **可能结果**：(50,90), (90,90)

#### T2: Repeatable Read

**T1: Repeatable Read**
- T2保证在事务内的读取一致性
- 如果sal1读到50，则sal2也读到50
- 如果sal1读到90，则sal2也读到90
- **可能结果**：(50,50), (90,90)

**T1: Serializable**
- 串行化执行，结果同上
- **可能结果**：(50,50), (90,90)

#### T2: Serializable

**T1: Repeatable Read 或 Serializable**
- 完全串行化执行
- T2要么完全在T1之前，要么完全在T1之后
- **可能结果**：(50,50), (90,90)

### MySQL特殊考虑

#### 当前读 vs 快照读
- **快照读**：普通SELECT使用MVCC，读取事务开始时的快照
- **当前读**：SELECT FOR UPDATE, UPDATE, DELETE等读取最新版本

在MySQL InnoDB中：
- Repeatable Read级别下，普通SELECT是快照读
- 快照读保证了事务内的一致性视图
- 这解释了为什么Repeatable Read级别下sal1和sal2相同

## 问题二：调度可串行化分析

### 调度分类标准

#### 可恢复性分类
1. **可恢复调度**：如果Ti读取了Tj写的数据，则Tj必须在Ti之前提交
2. **无级联调度**：事务只读取已提交事务写的数据
3. **严格调度**：事务只能读写已提交事务写的数据

#### 可串行化分类
1. **冲突可串行化**：通过交换非冲突操作可以变成串行调度
2. **视图可串行化**：与某个串行调度视图等价

### 选择的调度分析

#### 选择S_2：w_1(X), r_2(Y), r_1(Y), r_2(X)

**分析**：
- **无级联**：✓ 没有事务读取未提交事务的写入
- **冲突可串行化**：✓ 可以通过交换操作得到T1→T2的串行调度
- **视图可串行化**：✓ 与串行调度T1,T2视图等价

**理由**：这个调度展示了基本的并发执行，没有复杂的依赖关系

#### 选择S_5：r_1(X), w_2(X), w_1(X), abort(T_2), commit(T_1)

**分析**：
- **级联回滚**：✗ T1读取了T2写的X，但T2回滚了
- **不可恢复**：✗ T1在T2回滚前就可能提交
- **视图可串行化**：✗ 由于T2回滚，无法找到等价的串行调度

**理由**：这个调度展示了事务回滚导致的问题

#### 选择S_8：w_1(X), r_2(X), w_1(X), commit(T_2), commit(T_1)

**分析**：
- **级联**：✗ T2读取了T1未提交的写入
- **可恢复**：✓ T1在T2之后提交
- **冲突可串行化**：✗ 存在冲突环
- **视图可串行化**：需要详细分析

**理由**：这个调度展示了脏读但仍可恢复的情况

#### 选择S_11：r_1(X), w_2(X), commit(T_2), w_1(X), commit(T_1), r_3(X), commit(T_3)

**分析**：
- **无级联**：✓ 所有读操作都读取已提交的数据
- **冲突可串行化**：需要构建优先图分析
- **视图可串行化**：✓ 可以找到等价的串行调度

**理由**：这个调度展示了三个事务的复杂交互

### 优先图分析示例

以S_8为例：w_1(X), r_2(X), w_1(X), commit(T_2), commit(T_1)

**冲突操作**：
- w_1(X) → r_2(X)：T1 → T2
- r_2(X) → w_1(X)：T2 → T1

**优先图**：T1 ⇄ T2（存在环）

**结论**：不是冲突可串行化的

### 实际应用建议

1. **生产环境**：通常使用Read Committed或Repeatable Read
2. **高并发场景**：避免Serializable级别，性能影响大
3. **数据一致性要求高**：使用Repeatable Read + 适当的锁机制
4. **MySQL InnoDB**：默认Repeatable Read，通过MVCC实现高并发

### 总结

不同的隔离级别在并发控制中提供了不同的一致性保证：
- **Read Uncommitted**：最低隔离级别，可能出现脏读
- **Read Committed**：避免脏读，但可能不可重复读
- **Repeatable Read**：避免不可重复读，但可能幻读
- **Serializable**：最高隔离级别，完全串行化

选择合适的隔离级别需要在数据一致性和系统性能之间找到平衡点。

## 详细调度分析

### 从S_1~S_3中选择：S_2

**调度S_2**：w_1(X), r_2(Y), r_1(Y), r_2(X)

#### 可恢复性分析
- **读写依赖**：T2读取了T1写的X，但这发生在最后
- **提交顺序**：假设T1先提交，T2后提交
- **结论**：可恢复（满足Ti读Tj写的数据时，Tj在Ti前提交）

#### 无级联性分析
- T2的r_2(X)读取了T1的w_1(X)
- 如果T1回滚，T2需要级联回滚
- **结论**：不是无级联的

#### 冲突可串行化分析
**冲突操作识别**：
- w_1(X) 与 r_2(X)：写读冲突，T1 → T2
- r_1(Y) 与 r_2(Y)：读读不冲突

**优先图**：T1 → T2（无环）

**等价串行调度**：T1, T2

**结论**：冲突可串行化

#### 视图可串行化分析
**初始读**：无
**最终写**：T1写X，无人写Y
**中间读**：T2读取T1写的X

与串行调度T1,T2比较：
- 读写关系一致
- 最终写入一致

**结论**：视图可串行化

### 从S_4~S_6中选择：S_5

**调度S_5**：r_1(X), w_2(X), w_1(X), abort(T_2), commit(T_1)

#### 可恢复性分析
- T1读取初始X值
- T2写X后回滚
- T1写X后提交
- **问题**：T1的操作基于可能包含T2影响的状态

#### 无级联性分析
- T1没有读取T2写的数据（T1读的是初始值）
- **结论**：无级联

#### 可串行化分析
- T2回滚，相当于T2没有执行
- 等价于只有T1执行
- **结论**：可串行化（等价于串行调度T1）

**选择理由**：展示了事务回滚对调度性质的影响，虽然无级联但涉及回滚处理

### 从S_7~S_9中选择：S_8

**调度S_8**：w_1(X), r_2(X), w_1(X), commit(T_2), commit(T_1)

#### 可恢复性分析
- T2读取了T1写的X
- T1在T2之后提交
- **结论**：可恢复

#### 无级联性分析
- T2读取了T1未提交的写入
- 如果T1回滚，T2需要级联回滚
- **结论**：不是无级联的

#### 冲突可串行化分析
**冲突操作**：
- w_1(X) → r_2(X)：T1 → T2
- r_2(X) → w_1(X)：T2 → T1

**优先图**：T1 ⇄ T2（存在环）

**结论**：不是冲突可串行化的

#### 视图可串行化分析
需要检查是否存在等价的串行调度：
- 串行调度T1,T2：T2读到T1的最终写入
- 串行调度T2,T1：T2读到初始值

当前调度中T2读到T1的中间写入，这在任何串行调度中都不可能出现。

**结论**：不是视图可串行化的

**选择理由**：展示了脏读导致的不可串行化问题

### 从S_10~S_12中选择：S_11

**调度S_11**：r_1(X), w_2(X), commit(T_2), w_1(X), commit(T_1), r_3(X), commit(T_3)

#### 可恢复性分析
- 所有读操作都在相关写操作提交后进行
- **结论**：可恢复

#### 无级联性分析
- T3读取T1写的X，且T1已提交
- 没有读取未提交的写入
- **结论**：无级联

#### 冲突可串行化分析
**冲突操作**：
- r_1(X) → w_2(X)：T1 → T2
- w_2(X) → w_1(X)：T2 → T1
- w_1(X) → r_3(X)：T1 → T3

**优先图**：T1 ⇄ T2, T1 → T3

存在T1和T2之间的环，不是冲突可串行化的。

#### 视图可串行化分析
**最终写入**：T1写X
**读取关系**：
- T1读初始X
- T3读T1写的X

检查可能的串行调度：
- T1,T2,T3：T3读T2写的X（不匹配）
- T2,T1,T3：T3读T1写的X（匹配）
- 其他排列...

需要详细验证，但由于存在复杂的读写关系，可能不是视图可串行化的。

**选择理由**：展示了多事务环境下无级联但不可串行化的情况

## 总结表格

| 调度 | 可恢复 | 无级联 | 冲突可串行化 | 视图可串行化 | 选择理由 |
|------|--------|--------|--------------|--------------|----------|
| S_2  | ✓      | ✗      | ✓            | ✓            | 基本并发，展示级联问题 |
| S_5  | ?      | ✓      | ✓            | ✓            | 事务回滚的影响 |
| S_8  | ✓      | ✗      | ✗            | ✗            | 脏读导致不可串行化 |
| S_11 | ✓      | ✓      | ✗            | ?            | 多事务复杂交互 |

这些选择涵盖了并发控制中的主要问题：级联回滚、脏读、事务回滚和多事务交互。
