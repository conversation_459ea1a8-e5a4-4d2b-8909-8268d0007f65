# 数据库范式理论与分解算法分析

## 问题一：全主属性关系模式的最高范式

### 问题：一个全是主属性的关系模式最高一定可以达到第几范式？

**答案：3NF（第三范式）**

### 详细分析

#### 定义回顾
- **主属性**：包含在任何一个候选码中的属性
- **全主属性关系模式**：关系模式中的所有属性都是主属性

#### 范式分析

**1NF（第一范式）**：
- 要求：每个属性都是原子的，不可再分
- 全主属性关系模式显然满足1NF

**2NF（第二范式）**：
- 要求：满足1NF，且非主属性完全函数依赖于候选码
- 由于全主属性关系模式中没有非主属性，所以不存在部分函数依赖问题
- **一定满足2NF**

**3NF（第三范式）**：
- 要求：满足2NF，且非主属性不传递依赖于候选码
- 由于没有非主属性，不存在传递依赖问题
- **一定满足3NF**

**BCNF（BC范式）**：
- 要求：对于每个函数依赖X→Y，X必须是超码
- 全主属性关系模式可能存在主属性之间的函数依赖，而左边不是超码
- **不一定满足BCNF**

#### 反例说明

考虑关系模式R(A,B,C)，其中：
- 候选码：{A,B}和{A,C}
- 所有属性A,B,C都是主属性
- 函数依赖：B→C

分析：
- B→C中，B不是超码（B不能确定A）
- 因此不满足BCNF
- 但满足3NF（C是主属性）

**结论：全主属性关系模式最高一定可以达到3NF**

---

## 问题二：全码关系模式的最高范式

### 问题：一个全码的关系模式最高一定可以达到第几范式？

**答案：BCNF（BC范式）**

### 详细分析

#### 定义回顾
- **全码关系模式**：关系模式的所有属性构成唯一的候选码

#### 范式分析

**基本特征**：
- 只有一个候选码，就是所有属性的组合
- 所有属性都是主属性
- 不存在非平凡的函数依赖（除了平凡依赖）

**BCNF验证**：
- 对于任何非平凡函数依赖X→Y
- 由于是全码，X必须包含所有属性才能确定Y
- 因此X必须是超码
- **满足BCNF定义**

**4NF验证**：
- 可能存在多值依赖
- 不一定满足4NF

#### 示例

关系模式R(学号,课程号,教师号)，全码为{学号,课程号,教师号}
- 不存在非平凡函数依赖
- 满足BCNF
- 但可能存在多值依赖：学号→→课程号

**结论：全码关系模式最高一定可以达到BCNF**

---

## 问题三：二目关系模式与BCNF

### 问题：任何一个二目关系模式R(A,B)一定属于BCNF吗？

**答案：是的**

### 详细分析

#### 二目关系模式的可能情况

**情况1：无函数依赖**
- 候选码：{A,B}
- 满足BCNF（无非平凡函数依赖）

**情况2：A→B**
- 候选码：{A}
- 唯一的非平凡函数依赖A→B中，A是候选码，因此是超码
- 满足BCNF

**情况3：B→A**
- 候选码：{B}
- 唯一的非平凡函数依赖B→A中，B是候选码，因此是超码
- 满足BCNF

**情况4：A→B且B→A**
- 候选码：{A}和{B}
- 所有函数依赖的左边都是候选码
- 满足BCNF

#### 理论证明

对于二目关系模式R(A,B)：
- 可能的函数依赖只有：A→B, B→A, 或两者都有
- 任何非平凡函数依赖的左边必须是单个属性
- 如果X→Y成立，则X必须能唯一确定整个元组
- 因此X必须是候选码，即超码

**结论：任何二目关系模式都一定属于BCNF**

---

## 问题四：单候选码3NF与BCNF的关系

### 问题：一个只有一个候选码的3NF关系模式是BCNF的吗？

**答案：不一定**

### 详细分析

#### 反例构造

考虑关系模式R(A,B,C,D)：
- 候选码：{A,B}
- 函数依赖：C→D
- 其中C,D都是主属性（假设存在另一个候选码{C,D}使得C,D成为主属性）

等等，这个例子有问题。让我重新构造：

**正确的反例**：
关系模式R(学生,课程,教师)：
- 候选码：{学生,课程}
- 函数依赖：课程→教师
- 假设一门课程只能有一个教师

分析：
- 教师是非主属性
- 课程→教师，但课程不是超码
- 不满足BCNF
- 但满足3NF（教师直接依赖于候选码的一部分，但这是完全依赖）

**更清晰的反例**：
关系模式R(A,B,C)：
- 候选码：{A,B}
- 函数依赖：A→C
- C是非主属性

分析：
- A→C中，A不是超码（A不能确定B）
- 不满足BCNF
- 但C完全函数依赖于候选码{A,B}的子集，满足3NF

**结论：单候选码的3NF关系模式不一定是BCNF**

---

## 问题五：3NF设计和BCNF设计的优缺点

### 3NF设计

#### 优点
1. **保持函数依赖**：3NF分解算法能够保持所有函数依赖
2. **无损连接**：分解后的关系可以无损地连接回原关系
3. **实用性强**：大多数实际应用中3NF已经足够
4. **分解简单**：分解算法相对简单，易于实现

#### 缺点
1. **可能存在冗余**：仍可能存在一些数据冗余
2. **更新异常**：在某些情况下仍可能出现更新异常
3. **不是最高范式**：不是最严格的范式要求

### BCNF设计

#### 优点
1. **消除冗余**：更彻底地消除数据冗余
2. **避免更新异常**：更好地避免插入、删除、更新异常
3. **理论完美**：在函数依赖层面达到理论最优

#### 缺点
1. **可能丢失函数依赖**：BCNF分解可能无法保持所有函数依赖
2. **分解复杂**：分解算法更复杂
3. **过度规范化**：在某些实际应用中可能过度规范化，影响查询效率

### 选择建议

- **一般应用**：选择3NF，平衡了理论完美性和实用性
- **数据一致性要求极高**：选择BCNF
- **查询性能优先**：可能选择适度的反规范化

---

## 问题六：多值依赖和函数依赖的异同点

### 相同点

1. **都是数据依赖**：都描述属性间的约束关系
2. **都有传递性**：都满足传递律
3. **都影响范式**：都是数据库规范化的重要依据
4. **都可以推理**：都有相应的推理规则和公理系统

### 不同点

#### 定义层面
- **函数依赖**：X→Y表示X值确定时Y值唯一确定
- **多值依赖**：X→→Y表示X值确定时Y值集合确定，但Y的每个值可能对应多个Z值

#### 约束强度
- **函数依赖**：更强的约束，一对一或多对一关系
- **多值依赖**：较弱的约束，一对多关系

#### 范式要求
- **函数依赖**：主要影响1NF到BCNF
- **多值依赖**：主要影响4NF和5NF

#### 表示方法
- **函数依赖**：X→Y
- **多值依赖**：X→→Y

#### 实际含义
- **函数依赖**：值的唯一性约束
- **多值依赖**：值集合的独立性约束

### 关系
- 每个函数依赖都是多值依赖：X→Y ⟹ X→→Y
- 多值依赖是函数依赖的推广
- 在4NF中需要同时考虑函数依赖和多值依赖

---

## 问题七：BCNF分解算法的无损性保证

### BCNF分解算法步骤

1. **输入**：关系模式R和函数依赖集F
2. **初始化**：result := {R}
3. **迭代分解**：
   - 对result中每个不满足BCNF的关系模式Ri
   - 找到违反BCNF的函数依赖X→Y
   - 将Ri分解为R1(X∪Y)和R2(Ri-Y)
   - 用R1和R2替换Ri

### 无损性保证机制

#### 理论基础
**无损连接定理**：设关系模式R被分解为R1和R2，当且仅当以下条件之一成立时，分解是无损的：
- R1∩R2 → R1-R2 在F+中成立
- R1∩R2 → R2-R1 在F+中成立

#### BCNF分解中的应用

当我们根据函数依赖X→Y进行分解时：
- R1 = X∪Y
- R2 = R-Y
- R1∩R2 = X

**无损性验证**：
- 由于X→Y，我们有X→(X∪Y)-X = Y
- 即R1∩R2 → R1-R2成立
- 因此分解是无损的

#### 算法保证
1. **每次分解都是无损的**：基于违反BCNF的函数依赖进行分解
2. **递归保持无损性**：无损分解的传递性
3. **最终结果无损**：所有中间步骤都保持无损性

### 示例验证

关系模式R(A,B,C,D)，函数依赖{A→B, B→C}

**第一次分解**（基于A→B）：
- R1(A,B), R2(A,C,D)
- R1∩R2 = {A}
- A→B，即{A}→{B}成立，分解无损

**第二次分解**（R2基于传递得到的A→C）：
- R21(A,C), R22(A,D)
- 分解继续保持无损

**结论：BCNF分解算法通过基于函数依赖的分解策略和无损连接定理保证分解的无损性**

---

## 问题八：3NF分解算法第3步的3NF确认

### 3NF分解算法回顾

**算法步骤**：
1. 求F的最小函数依赖集Fm
2. 对Fm中每个函数依赖X→Y，构造关系模式(X∪Y)
3. 如果没有一个关系模式包含R的候选码，则添加一个包含R的任意候选码的关系模式
4. 删除被其他关系模式包含的关系模式

### 第3步后为什么一定是3NF

#### 理论依据

**3NF定义**：关系模式R属于3NF当且仅当对于F+中每个函数依赖X→A，下列条件之一成立：
1. A∈X（平凡依赖）
2. X是R的超码
3. A是R的主属性

#### 算法构造的关系模式特点

**步骤2构造的关系模式**：
- 每个关系模式Ri对应一个函数依赖Xi→Yi
- Ri = Xi∪Yi

**3NF验证**：
对于Ri中的任何函数依赖Z→B：

**情况1**：Z→B是原始依赖Xi→Yi的子集
- 由于Ri是基于Xi→Yi构造的，这种依赖满足3NF

**情况2**：Z→B是新产生的依赖
- 如果Z不是超码，则B必须是主属性
- 这是因为算法第3步确保了每个关系模式要么包含完整的函数依赖，要么包含候选码

**步骤3添加的关系模式**：
- 包含原关系R的候选码
- 自动满足3NF（候选码的任何子集到其他属性的依赖都满足3NF条件）

#### 关键洞察

1. **依赖保持**：每个原始函数依赖都在某个分解后的关系中完整保持
2. **候选码保持**：确保至少有一个关系包含原关系的候选码
3. **最小性**：使用最小函数依赖集避免冗余

#### 形式化证明思路

设分解后的关系模式为R1, R2, ..., Rn

**对于任意Ri和其中的函数依赖X→A**：
- 如果X→A来自原始依赖集，则Ri是基于此依赖构造的，满足3NF
- 如果X→A是推导出的，则要么X是Ri的超码，要么A是主属性

**主属性的确定**：
- 通过步骤3确保候选码的存在
- 主属性在相应的关系模式中得到正确识别

**结论：3NF分解算法的第3步通过确保候选码的保持和依赖的完整性，保证了所有分解后的关系模式都满足3NF**

---

## 实践题目解答

### 题目一：R(ABCDE)的候选码、范式级别和分解

#### 给定条件
- 关系模式：R(ABCDE)
- 函数依赖集：F = {AB → C, B → D, CD → E, CE → B, AC → B}

#### 步骤1：求候选码

**方法：计算属性闭包**

**分析各属性的作用**：
- A：只出现在依赖的左边，是必需属性
- B：既出现在左边也出现在右边
- C：既出现在左边也出现在右边
- D：既出现在左边也出现在右边
- E：既出现在左边也出现在右边

**计算候选码**：
从必需属性A开始，逐步添加其他属性：

**(A)+ = {A}** - 不足以确定所有属性

**(AB)+ 计算**：
- 初始：{A, B}
- 由AB → C：{A, B, C}
- 由B → D：{A, B, C, D}
- 由CD → E：{A, B, C, D, E}

**(AB)+ = {A, B, C, D, E} = R**

验证AB是最小的：
- (A)+ = {A} ≠ R
- (B)+ = {B, D} ≠ R

**候选码：{AB}**

#### 步骤2：判断范式级别

**1NF检查**：满足（假设所有属性都是原子的）

**2NF检查**：
- 候选码：{AB}
- 非主属性：{C, D, E}
- 检查部分函数依赖：
  - B → D：B是候选码的真子集，D是非主属性
  - **存在部分函数依赖，不满足2NF**

**结论：R属于1NF，不属于2NF**

#### 步骤3：保持无损连接和函数依赖的分解

**方法1：基于2NF的分解**

由于B → D违反2NF，进行分解：
- R1(B, D)
- R2(A, B, C, E)

**检查R2是否满足2NF**：
在R2中，候选码仍是{AB}，检查剩余依赖：
- AB → C：完全函数依赖，满足2NF
- CE → B：C, E都不是候选码的子集

R2满足2NF，继续检查3NF：
- CE → B中，CE不是超码，B是主属性，满足3NF

**最终分解**：
- R1(B, D)：满足BCNF
- R2(A, B, C, E)：满足3NF

**验证无损连接**：
- R1 ∩ R2 = {B}
- B → D在F+中成立
- 分解是无损的

**验证函数依赖保持**：
- AB → C：在R2中保持
- B → D：在R1中保持
- CD → E：需要通过连接R1和R2来验证
- CE → B：在R2中保持
- AC → B：在R2中保持

**方法2：3NF分解算法**

1. **求最小函数依赖集**：
   - AB → C
   - B → D
   - CD → E
   - CE → B
   - AC → B

2. **为每个依赖构造关系模式**：
   - R1(A, B, C)：基于AB → C
   - R2(B, D)：基于B → D
   - R3(C, D, E)：基于CD → E
   - R4(C, E, B)：基于CE → B
   - R5(A, C, B)：基于AC → B

3. **消除冗余**：
   - R5被R1包含，删除R5
   - R4被R1∪R3包含，删除R4

4. **检查候选码**：
   - 候选码{AB}包含在R1中

**最终3NF分解**：
- R1(A, B, C)
- R2(B, D)
- R3(C, D, E)

---

### 题目二：函数依赖在子关系上的投影

#### 给定条件
- 原关系：R(ABCDE)
- 函数依赖集：F = {AB → D, AC → E, BC → D, D → A, E → B}
- 目标关系：S(ABCD)

#### 投影算法

**定义**：F在S上的投影πS(F)是所有形如X → Y的函数依赖，其中X, Y ⊆ S且X → Y ∈ F+

**步骤1：找出所有可能的左边**
S的所有非空子集作为可能的左边：
{A}, {B}, {C}, {D}, {AB}, {AC}, {AD}, {BC}, {BD}, {CD}, {ABC}, {ABD}, {ACD}, {BCD}, {ABCD}

**步骤2：计算每个子集在F下的闭包，然后与S求交**

**(A)+F 计算**：
- 初始：{A}
- 无直接依赖从A开始
- (A)+F = {A}
- (A)+F ∩ S = {A}
- 得到：A → A（平凡依赖）

**(B)+F 计算**：
- 初始：{B}
- 无直接依赖从B开始
- (B)+F = {B}
- (B)+F ∩ S = {B}

**(C)+F 计算**：
- 初始：{C}
- (C)+F = {C}
- (C)+F ∩ S = {C}

**(D)+F 计算**：
- 初始：{D}
- 由D → A：{D, A}
- (D)+F = {D, A}
- (D)+F ∩ S = {D, A}
- 得到：D → A

**(AB)+F 计算**：
- 初始：{A, B}
- 由AB → D：{A, B, D}
- (AB)+F = {A, B, D}
- (AB)+F ∩ S = {A, B, D}
- 得到：AB → D, AB → A, AB → B（后两个是平凡的）

**(AC)+F 计算**：
- 初始：{A, C}
- 由AC → E：{A, C, E}
- 由E → B：{A, C, E, B}
- 由AB → D：{A, C, E, B, D}
- (AC)+F ∩ S = {A, B, C, D}
- 得到：AC → B, AC → D, AC → ABCD

**(BC)+F 计算**：
- 初始：{B, C}
- 由BC → D：{B, C, D}
- 由D → A：{B, C, D, A}
- (BC)+F ∩ S = {A, B, C, D}
- 得到：BC → A, BC → D, BC → ABCD

**继续计算其他组合...**

**最终投影结果**：
πS(F) = {D → A, AB → D, AC → B, AC → D, BC → A, BC → D}

**最小化后**：
πS(F) = {D → A, AB → D, BC → D, AC → B}

---

### 题目三：R(BCDFGH)的3NF分解

#### 给定条件
- 关系模式：R(BCDFGH)
- 函数依赖集：F = {BG → CD, G → F, CD → GH, C → FG, F → D}

#### 步骤1：求候选码

**分析属性**：
- B：只在左边出现，是必需属性
- 其他属性都既在左边又在右边出现

**计算(B)+**：
- 初始：{B}
- 无法推导出其他属性
- (B)+ = {B}

**计算(BG)+**：
- 初始：{B, G}
- 由BG → CD：{B, G, C, D}
- 由G → F：{B, G, C, D, F}
- 由CD → GH：{B, G, C, D, F, H}（H已包含）
- 由C → FG：无新属性
- 由F → D：无新属性
- (BG)+ = {B, C, D, F, G, H} = R

**验证最小性**：
- (B)+ ≠ R
- (G)+ = {G, F} ≠ R

**候选码：{BG}**

#### 步骤2：3NF分解算法

**第1步：求最小函数依赖集**
检查每个依赖是否可以简化：
- BG → CD：不能分解为更小的依赖
- G → F：已经最小
- CD → GH：不能分解
- C → FG：不能分解
- F → D：已经最小

最小依赖集：Fm = {BG → CD, G → F, CD → GH, C → FG, F → D}

**第2步：为每个依赖构造关系模式**
- R1(B, G, C, D)：基于BG → CD
- R2(G, F)：基于G → F
- R3(C, D, G, H)：基于CD → GH
- R4(C, F, G)：基于C → FG
- R5(F, D)：基于F → D

**第3步：检查候选码包含**
候选码{BG}包含在R1中

**第4步：消除冗余关系**
- R2(G, F)被R4(C, F, G)包含，删除R2
- R5(F, D)不被其他关系包含，保留

**最终3NF分解**：
- R1(B, G, C, D)
- R3(C, D, G, H)
- R4(C, F, G)
- R5(F, D)

#### 验证

**无损连接验证**：
使用连接图或表格方法验证分解的无损性

**函数依赖保持验证**：
- BG → CD：在R1中保持
- G → F：通过R4中的C → FG和连接保持
- CD → GH：在R3中保持
- C → FG：在R4中保持
- F → D：在R5中保持

**3NF验证**：
每个关系模式都满足3NF要求

---

### 题目四：R(ABCD)的范式级别判断

#### 给定条件
- 关系模式：R(ABCD)
- 函数依赖：A → BCD
- 多值依赖：B →→ C

#### 步骤1：分析隐藏的函数依赖

**从A → BCD可以推导出**：
- A → B
- A → C
- A → D

**关键洞察**：由于A → BCD，A可以唯一确定B和C的值
- 如果A确定了B的值，B →→ C就变成了函数依赖
- 因为A → B且A → C，所以对于任何给定的A值，B和C都是唯一确定的
- **隐藏的函数依赖：B → C**

#### 步骤2：完整的依赖集

**函数依赖**：
- A → B
- A → C
- A → D
- B → C（从多值依赖推导出的函数依赖）

**多值依赖**：
- B →→ C（但实际上是函数依赖）

#### 步骤3：求候选码

**(A)+ 计算**：
- 初始：{A}
- 由A → BCD：{A, B, C, D}
- (A)+ = {A, B, C, D} = R

**候选码：{A}**

#### 步骤4：范式级别判断

**1NF**：满足

**2NF**：
- 候选码：{A}（单属性）
- 不存在部分函数依赖
- 满足2NF

**3NF**：
- 所有函数依赖的左边都是候选码A或其超集
- 满足3NF

**BCNF**：
- 检查B → C：B不是超码
- **不满足BCNF**

**4NF**：
- 原本的多值依赖B →→ C实际上是函数依赖B → C
- 不存在非平凡的多值依赖
- **满足4NF**

**结论：R属于4NF，但不属于BCNF**

这是一个特殊情况，说明4NF和BCNF不是严格的包含关系。

---

### 题目五：判断多值依赖A →→ B的关系代数和SQL

#### 多值依赖A →→ B的定义

在关系r(ABC)中，A →→ B成立当且仅当：
对于任意两个元组t1, t2 ∈ r，如果t1[A] = t2[A]，则存在元组t3, t4 ∈ r使得：
- t3[A] = t4[A] = t1[A] = t2[A]
- t3[B] = t1[B], t3[C] = t2[C]
- t4[B] = t2[B], t4[C] = t1[C]

#### 关系代数表达式

```
π_A,B,C(r) = π_A,B(r) ⋈ π_A,C(r)
```

**解释**：
- π_A,B(r)：投影得到所有(A,B)组合
- π_A,C(r)：投影得到所有(A,C)组合
- 自然连接：对于相同的A值，产生所有B和C的组合
- 如果结果等于原关系r，则A →→ B成立

#### SQL语句

**方法1：使用NOT EXISTS检查**

```sql
-- 检查A →→ B是否成立
SELECT CASE
    WHEN NOT EXISTS (
        -- 查找违反多值依赖的情况
        SELECT 1
        FROM r r1, r r2
        WHERE r1.A = r2.A
        AND NOT EXISTS (
            SELECT 1 FROM r r3, r r4
            WHERE r3.A = r1.A
            AND r4.A = r1.A
            AND r3.B = r1.B
            AND r3.C = r2.C
            AND r4.B = r2.B
            AND r4.C = r1.C
        )
    ) THEN 'A →→ B 成立'
    ELSE 'A →→ B 不成立'
END AS result;
```

**方法2：使用集合比较**

```sql
-- 检查投影连接是否等于原关系
WITH projected_join AS (
    SELECT DISTINCT ab.A, ab.B, ac.C
    FROM (SELECT DISTINCT A, B FROM r) ab
    CROSS JOIN (SELECT DISTINCT A, C FROM r) ac
    WHERE ab.A = ac.A
),
original_relation AS (
    SELECT DISTINCT A, B, C FROM r
)
SELECT CASE
    WHEN (
        SELECT COUNT(*) FROM projected_join
        EXCEPT
        SELECT COUNT(*) FROM original_relation
    ) = 0
    AND (
        SELECT COUNT(*) FROM original_relation
        EXCEPT
        SELECT COUNT(*) FROM projected_join
    ) = 0
    THEN 'A →→ B 成立'
    ELSE 'A →→ B 不成立'
END AS result;
```

**方法3：简化的检查方法**

```sql
-- 检查是否存在违反多值依赖的元组对
SELECT CASE
    WHEN COUNT(*) = 0 THEN 'A →→ B 成立'
    ELSE 'A →→ B 不成立'
END AS result
FROM (
    SELECT r1.A, r1.B, r1.C, r2.B as B2, r2.C as C2
    FROM r r1, r r2
    WHERE r1.A = r2.A
    AND (r1.B != r2.B OR r1.C != r2.C)
    AND NOT EXISTS (
        SELECT 1 FROM r r3
        WHERE r3.A = r1.A
        AND r3.B = r1.B
        AND r3.C = r2.C
    )
) violations;
```

---

### 题目六：函数依赖集的最小覆盖

#### 给定条件
函数依赖集：F = {ABCD → E, E → D, A → B, AC → D}

#### 最小覆盖算法

**步骤1：右边单一化**
F已经满足右边单一化要求

**步骤2：消除左边冗余属性**

**检查ABCD → E**：
- 检查(ABC)+ 是否包含E
- (ABC)+ 计算：
  - 初始：{A, B, C}
  - 由A → B：无新属性（B已有）
  - 由AC → D：{A, B, C, D}
  - 由E → D：无法使用（E不在闭包中）
  - (ABC)+ = {A, B, C, D}
- E ∉ (ABC)+，所以D不能从ABCD → E中删除

- 检查(ACD)+ 是否包含E
- (ACD)+ 计算：
  - 初始：{A, C, D}
  - 由A → B：{A, B, C, D}
  - 由AC → D：无新属性
  - (ACD)+ = {A, B, C, D}
- E ∉ (ACD)+，所以B不能删除

- 检查(ABD)+ 是否包含E
- (ABD)+ 计算：
  - 初始：{A, B, D}
  - 由A → B：无新属性
  - 无其他可用依赖
  - (ABD)+ = {A, B, D}
- E ∉ (ABD)+，所以C不能删除

- 检查(BCD)+ 是否包含E
- (BCD)+ 计算：
  - 初始：{B, C, D}
  - 无可用依赖
  - (BCD)+ = {B, C, D}
- E ∉ (BCD)+，所以A不能删除

**ABCD → E无法简化**

**检查AC → D**：
- 检查(A)+ 是否包含D
- (A)+ = {A, B}（由A → B）
- D ∉ (A)+，所以C不能删除
- 检查(C)+ 是否包含D
- (C)+ = {C}
- D ∉ (C)+，所以A不能删除

**AC → D无法简化**

**步骤2结果**：F = {ABCD → E, E → D, A → B, AC → D}

**步骤3：消除冗余依赖**

**检查E → D是否冗余**：
在F' = {ABCD → E, A → B, AC → D}中计算(E)+：
- (E)+ = {E}
- D ∉ (E)+，所以E → D不冗余

**检查A → B是否冗余**：
在F' = {ABCD → E, E → D, AC → D}中计算(A)+：
- (A)+ = {A}
- B ∉ (A)+，所以A → B不冗余

**检查AC → D是否冗余**：
在F' = {ABCD → E, E → D, A → B}中计算(AC)+：
- 初始：{A, C}
- 由A → B：{A, B, C}
- (AC)+ = {A, B, C}
- D ∉ (AC)+，所以AC → D不冗余

**检查ABCD → E是否冗余**：
在F' = {E → D, A → B, AC → D}中计算(ABCD)+：
- 初始：{A, B, C, D}
- 由A → B：无新属性
- 由AC → D：无新属性
- 由E → D：无法使用（E不在闭包中）
- (ABCD)+ = {A, B, C, D}
- E ∉ (ABCD)+，所以ABCD → E不冗余

**最小覆盖**：
Fm = {ABCD → E, E → D, A → B, AC → D}

**结论：原函数依赖集已经是最小覆盖**

---

*本文档详细分析了数据库范式理论中的关键问题，涵盖了从基本范式概念到高级分解算法的理论基础和实践应用，并通过具体题目展示了理论在实际问题中的应用。*
