# 第3章 关系模型 - 答案

## E.<PERSON>. Codd与关系模型基础

### 1. 请概述E.F. Codd在关系数据模型方面的贡献。
E.F. Codd（埃德加·弗兰克·科德）是关系数据库理论的奠基人，主要贡献包括：

- **提出关系模型**：1970年发表论文首次提出关系数据模型
- **建立理论基础**：为关系数据库提供了坚实的数学理论基础
- **定义关系代数和关系演算**：提供了关系操作的理论框架
- **制定完整性规则**：提出实体完整性、参照完整性等概念
- **提出规范化理论**：建立了数据库设计的规范化方法
- **制定全关系系统准则**：定义了真正关系数据库系统应满足的12条准则

### 2. E.F. Codd在关系数据理论方面发表了哪些代表性的论文？
主要代表性论文包括：

- **1970年**：《大型共享数据库的关系模型》（A Relational Model of Data for Large Shared Data Banks）- 奠基性论文
- **1972年**：《关系完整性》（Relational Completeness of Data Base Sublanguages）
- **1979年**：《扩展关系模型RM/T》（Extending the Database Relational Model to Capture More Meaning）
- **1985年**：《关系数据库管理系统是否真正关系化？》（Is Your DBMS Really Relational?）
- **1990年**：《关系模型版本2》（The Relational Model for Database Management Version 2）

### 3. 如何理解关系是笛卡尔积的有意义的子集？"有意义"体现在哪些方面？
**笛卡尔积**：两个集合A和B的笛卡尔积A×B是所有可能的有序对(a,b)的集合。

**"有意义"体现在**：
- **语义相关性**：元组中的属性值之间存在现实世界的语义联系
- **完整性约束**：满足实体完整性、参照完整性等约束条件
- **业务规则**：符合特定的业务逻辑和规则
- **数据一致性**：属性值之间保持逻辑一致性

**判断方法**：
- 检查是否满足完整性约束
- 验证业务规则的合理性
- 确认数据的语义一致性
- 评估实际应用价值

### 4. 在关系模型中，关系具有哪些性质？
- **列的同质性**：每一列中的分量是同一类型的数据
- **不同列可出自同一个域**：不同属性可以建立在同一个域上
- **列的顺序无关紧要**：列的次序可以任意交换
- **任意两个元组不能完全相同**：关系实际上是一个集合
- **行的顺序无关紧要**：元组的次序可以任意交换
- **分量必须取原子值**：每个分量都是不可分的数据项

### 5. 在关系模型中，什么是候选码？什么是外码？它们各自的作用是什么？
**候选码（Candidate Key）**：
- 定义：能唯一标识关系中每个元组的属性或属性组合
- 作用：确保实体完整性，提供唯一标识机制

**外码（Foreign Key）**：
- 定义：关系中引用其他关系主码的属性或属性组合
- 作用：维护参照完整性，建立表间联系

### 6. 从型和值的角度来阐述关系数据库的构成包括哪些方面？
**关系模式（型）**：
- 关系模式的定义
- 属性的域定义
- 完整性约束规则
- 数据依赖关系

**关系（值）**：
- 具体的数据实例
- 元组的集合
- 属性值的具体取值
- 满足完整性约束的数据状态

## 关系操作语言

### 7. 关系模型操作语言的特点是什么？
- **非过程化**：只需说明要什么，不需说明怎么做
- **面向集合**：一次操作处理整个关系（元组集合）
- **数学基础**：基于关系代数和关系演算的数学理论
- **完备性**：具有关系完备性，能表达所有可能的查询
- **优化潜力**：为查询优化提供了理论基础

### 8. 关于关系模型的操作语言，请详细阐述什么是关系代数？什么是关系验算？
**关系代数**：
- 定义：一种过程化的查询语言，使用代数运算符操作关系
- 特点：指定操作序列，描述如何获得结果
- 基本操作：选择、投影、并、差、笛卡尔积
- 扩展操作：连接、除法、交等

**关系演算**：
- 定义：一种非过程化的查询语言，基于数理逻辑
- 特点：描述所需结果的特征，不指定操作步骤
- 类型：元组关系演算、域关系演算
- 基础：一阶谓词逻辑

**比较方面**：
- 过程化程度
- 表达能力
- 优化难易程度
- 实现复杂性

### 9. 如何比较关系代数和关系演算的优缺点？
**关系代数**：
- 优点：操作步骤明确，易于理解和实现，便于查询优化
- 缺点：需要指定操作序列，表达复杂查询较繁琐

**关系演算**：
- 优点：表达自然，接近自然语言，高度非过程化
- 缺点：实现复杂，优化困难，需要复杂的求解算法

### 10. 有人说关系验算比关系代数更具有非过程化的特点，如何理解这一说法？
**关系演算的非过程化特点**：
- **描述性**：只描述结果应满足的条件，不指定获取步骤
- **声明式**：用逻辑公式声明查询条件
- **抽象性**：不涉及具体的操作序列和算法
- **自然性**：更接近人类的思维方式和自然语言表达

**关系代数的过程化特点**：
- 需要指定具体的操作步骤
- 必须考虑操作的顺序
- 类似于程序设计中的算法描述

## 关系模型完整性

### 11. 关系模型的完整性包括哪几个方面？请详细阐述每个方面的内容。
**实体完整性**：
- 主码不能为空值
- 确保每个实体都能被唯一标识
- 防止出现无法区分的实体

**参照完整性**：
- 外码要么为空，要么必须是被参照关系中某个元组的主码值
- 维护表间的引用关系
- 防止出现悬挂引用

**用户定义完整性**：
- 根据具体应用需求定义的约束条件
- 包括域约束、检查约束等
- 反映具体应用的业务规则

### 12. Codd规定关系模型要满足1NF性质，它带来哪些具体的好处？如果不满足1NF，会有哪些潜在影响？
**1NF的好处**：
- **简化操作**：统一的原子值操作
- **避免歧义**：消除数据表示的多义性
- **便于索引**：原子值易于建立索引
- **支持查询优化**：为查询优化提供基础

**不满足1NF的影响**：
- **操作复杂**：需要特殊的操作处理复合值
- **查询困难**：难以进行精确查询和匹配
- **维护困难**：更新和删除操作变得复杂
- **性能下降**：影响查询和索引性能

## 关系代数操作

### 13. 关系代数的基本操作包括哪些？扩展操作包括哪些？如何用基本操作定义扩展操作？

**基本操作**：
- **选择（σ）**：从关系中选择满足条件的元组
- **投影（π）**：从关系中选择指定的属性列
- **并（∪）**：两个关系的并集
- **差（-）**：两个关系的差集
- **笛卡尔积（×）**：两个关系的笛卡尔积

**扩展操作**：
- **交（∩）**：两个关系的交集
- **连接（⋈）**：基于条件的关系连接
- **除法（÷）**：关系除法运算
- **外连接**：左外连接、右外连接、全外连接

**用基本操作定义扩展操作**：
- **交**：R ∩ S = R - (R - S)
- **θ连接**：R ⋈θ S = σθ(R × S)
- **自然连接**：R ⋈ S = πA(σF(R × S))，其中F是公共属性相等的条件
- **除法**：R ÷ S = πX(R) - πX((πX(R) × S) - R)

## 关系演算

### 14. 什么是元组关系验算？请列举几个查询例子。

**元组关系演算**：
- 以元组变量作为谓词变量的关系演算
- 基本形式：{t | P(t)}，表示所有使谓词P为真的元组t的集合

**查询例子**：

**例1**：查询所有学生信息
```
{t | Student(t)}
```

**例2**：查询年龄大于20的学生姓名
```
{t.name | Student(t) ∧ t.age > 20}
```

**例3**：查询选修了"数据库"课程的学生姓名
```
{t.name | Student(t) ∧ ∃s(SC(s) ∧ s.sno = t.sno ∧ s.cname = "数据库")}
```

**例4**：查询选修了所有课程的学生姓名
```
{t.name | Student(t) ∧ ∀c(Course(c) → ∃s(SC(s) ∧ s.sno = t.sno ∧ s.cno = c.cno))}
```

### 15. 什么是域关系验算？请列举几个查询例子。

**域关系演算**：
- 以域变量作为谓词变量的关系演算
- 基本形式：{<x1, x2, ..., xn> | P(x1, x2, ..., xn)}

**查询例子**：

**例1**：查询所有学生的学号和姓名
```
{<x, y> | Student(x, y, z, w)}
```

**例2**：查询年龄大于20的学生姓名
```
{<y> | Student(x, y, z, w) ∧ z > 20}
```

**例3**：查询选修了"数据库"课程的学生学号
```
{<x> | ∃y∃z(SC(x, y, z) ∧ Course(y, "数据库", w))}
```

## 全关系系统

### 16. 什么是全关系系统？
全关系系统是Codd提出的理想关系数据库系统标准，必须满足12条准则。这些准则定义了一个真正的关系数据库管理系统应该具备的特征，确保系统完全基于关系模型的理论基础。

### 17. 目前最新版本的MySQL不满足全关系系统十二条准则的哪些准则？
MySQL不完全满足的准则包括：
- **准则1（信息准则）**：支持非关系型存储引擎
- **准则3（空值处理）**：空值处理不够系统化
- **准则6（视图更新）**：复杂视图更新支持有限
- **准则8（物理数据独立性）**：存储引擎相关的物理特性
- **准则9（逻辑数据独立性）**：模式变更的独立性有限
- **准则12（无破坏准则）**：允许绕过关系接口的操作

### 18. 在Codd的全关系系统准则中，如何理解准则0？
**准则0：关系特征**
"一个关系型的DBMS必须完全通过它的关系能力来管理数据"

**理解要点**：
- 系统必须完全基于关系模型
- 所有数据管理功能都通过关系操作实现
- 不能依赖非关系型的数据访问方式
- 确保系统的关系完整性和一致性

### 19. 在Codd提出的全关系系统准则中，如何理解准则1：信息准则？
**准则1：信息准则**
"关系数据库中的所有信息都应该在逻辑级上用表中的值这一种方法显式地表示"

**理解要点**：
- 所有数据都以表格形式存储
- 元数据也应该以关系形式表示
- 不允许隐式的信息表示方法
- 确保信息表示的统一性和透明性

### 20. 在Codd的全关系系统准则中，如何理解准则3：空值的系统化处理？
**准则3：空值的系统化处理**
"空值必须在所有类型的数据中得到系统化的支持，并且独立于数据类型"

**理解要点**：
- 空值必须与所有数据类型兼容
- 空值的语义必须一致和明确
- 空值的处理必须系统化，不能随意
- 空值在逻辑运算中的行为必须明确定义

### 21. 在Codd的全关系系统准则中，如何理解准则6：视图更新准则？
**准则6：视图更新准则**
"所有理论上可更新的视图都应该可以由系统更新"

**理解要点**：
- 系统应该支持尽可能多的视图更新操作
- 更新操作应该能够正确传播到基表
- 系统应该能够判断视图是否可更新
- 提供明确的视图更新语义和规则

### 22. 在Codd的全关系系统准则中，如何理解准则10：数据完整性的独立性？
**准则10：数据完整性的独立性**
"完整性约束必须可以用关系数据库子语言定义，并且可以存储在目录中"

**理解要点**：
- 完整性约束应该与应用程序分离
- 约束定义应该存储在数据字典中
- 约束检查应该由DBMS自动执行
- 支持动态的约束定义和修改

### 23. 在Codd的全关系系统准则中，如何理解准则12：无破坏准则？
**准则12：无破坏准则**
"如果系统具有低级语言，那么这个低级语言不能绕过完整性规则"

**理解要点**：
- 所有数据访问都必须遵守完整性约束
- 不允许绕过关系接口直接操作数据
- 系统的所有层次都必须维护数据完整性
- 确保数据的一致性和可靠性

### 24. SQL Server（MySQL）在哪些方面不符合准则12：无破坏准则？
**不符合的方面**：
- **直接文件访问**：允许直接操作数据文件
- **存储过程绕过**：某些存储过程可能绕过约束检查
- **批量导入**：批量数据导入时可能跳过约束检查
- **系统表操作**：允许直接修改系统表
- **引擎层操作**：存储引擎层的直接操作可能绕过SQL层的约束

---

*本文档详细整理了第3章关系模型的核心概念、理论基础和Codd的贡献，涵盖了关系代数、关系演算、完整性约束和全关系系统准则等重要内容。*
